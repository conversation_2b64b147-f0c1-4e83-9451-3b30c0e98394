{"@@locale": "en", "trading_price_list": "Price list", "trading_category": "Category", "trading_command_history": "Command history", "trading_utilities": "Utilities", "trading_place_stock_order": "Place a stock order", "trading_buy_order": "Buy order", "trading_sell_order": "Sell order", "trading_buy": "Buy", "trading_sell": "<PERSON>ll", "trading_ordinary": "TK thường", "trading_ordinary_full": "<PERSON><PERSON><PERSON><PERSON> thư<PERSON>", "trading_deposit": "TK ký quỹ", "trading_deposit_full": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n ký quỹ", "trading_sCode": "Stock code", "trading_place_order_price": "Giá", "trading_stock": "Stock", "trading_normal": "Normal", "trading_transaction_history": "Transaction history", "trading_order_type_lo": "<PERSON><PERSON><PERSON>", "trading_order_type_buyin": "Lệnh Buy-in", "trading_order_type_ato": "Lệnh ATO", "trading_order_type_atc": "Lệnh ATC", "trading_order_type_mp": "<PERSON><PERSON><PERSON>", "trading_order_type_mak": "Lệnh MAK", "trading_order_type_mok": "Lệnh MOK", "trading_order_type_mtl": "Lệnh MTL", "trading_order_type_plo": "Lệnh PLO", "trading_order_type_condition": "<PERSON><PERSON><PERSON> đi<PERSON> kiện", "trading_order_type_gtc": "Lệnh GTC", "trading_no_filter": "<PERSON><PERSON><PERSON><PERSON> có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại", "trading_no_data": "<PERSON><PERSON><PERSON> tại không có dữ liệu", "trading_code_title": "Mã", "trading_bid_price_title": "Giá đặt/\nGiá khớp", "trading_vol_title": "KL đặt/\nKL khớp", "trading_status_title": "<PERSON><PERSON><PERSON><PERSON> thái", "trading_waiting_send": "Wait for send", "trading_sending": "Sending", "trading_party_trade": "Matched", "trading_waiting": "Waiting", "trading_edited": "Edited", "trading_cancelled": "Cancelled", "trading_cancelled_2": "Cancelled", "trading_sent": "<PERSON><PERSON>", "trading_matched_partial": "Partially matched", "trading_match_all": "Match all", "trading_rejected": "Rejected", "trading_cancelling": "Cancelling", "trading_modifying": "Modifying", "trading_modified": "Modified", "trading_expired": "Expired", "trading_expire": "Expire", "trading_sub_command": "Sub command", "trading_command_status": "Command status", "trading_sub_account": "Sub-account", "trading_history_stock_code": "Stock code", "trading_stock_type": "Stock type", "trading_command_type": "Command type", "trading_order_price": "Order price", "trading_order_time": "Order time", "trading_joint_volume": "Joint volume", "trading_average_matching_price": "Average matching price", "trading_order_value_2": "Order value", "trading_order_value": "Order value", "trading_value": "<PERSON><PERSON><PERSON> trị", "trading_status": "Status", "trading_edit_order": "Edit order", "trading_cancel_order": "Cancel order", "trading_price_price": "<PERSON><PERSON><PERSON>", "trading_selected": "Selected", "trading_waiting_to_send": "Wait for send", "trading_save_command": "<PERSON><PERSON><PERSON> thông tin lệnh cho lần sau", "trading_month": "month", "trading_year": "year", "trading_custom": "Custom", "trading_sub_account_hint": "Sub-account", "trading_status_hint": "Status", "trading_no_data_message": "No data", "trading_cancel_order_success": "Order cancelled successfully", "trading_update_order_success": "Order updated successfully", "trading_matched_price": "Matched price", "trading_order_value_title": "Order value", "trading_cancel_order_title": "Cancel order", "trading_cancel_order_confirm_message": "Are you sure you want to cancel the selected order?", "trading_close": "Close", "trading_buy_order_title": "Buy order", "trading_buying_power": "Buying power", "trading_price": "Price", "trading_volume": "Volume", "trading_max_volume": "Maximum buy volume", "trading_max_volume_sell": "Maximum sell volume", "trading_edit_order_button": "Edit order", "trading_status_filter_title": "Status", "trading_all": "All", "trading_reset": "Reset", "trading_apply": "Apply", "trading_search_by_stock_code": "Search by stock code", "trading_transaction_type": "Transaction type", "trading_order_type_filter": "Order Type", "trading_time": "Time", "trading_waiting_match": "Waiting for match", "trading_processing": "Processing", "trading_matched_complete": "Matched", "trading_status_all": "Status: All", "trading_status_selected": "Status: Selected", "trading_pending_order": "List of pending orders", "trading_order_description_ato": "<PERSON><PERSON><PERSON> tranh mua bán tại mức giá mở cửa", "trading_order_description_mp": "<PERSON><PERSON><PERSON> đặt mua - b<PERSON> chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch", "trading_order_description_mak": "<PERSON><PERSON><PERSON> thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh", "trading_order_description_mok": "<PERSON><PERSON><PERSON> thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập", "trading_order_description_mtl": "<PERSON><PERSON><PERSON> thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO", "trading_order_description_lo": "<PERSON><PERSON><PERSON> đặt mua - b<PERSON> chứng khoán theo giá mong muốn", "trading_order_description_buyin": "<PERSON><PERSON><PERSON> bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00", "trading_order_description_atc": "<PERSON><PERSON><PERSON> mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa", "trading_order_description_plo": "<PERSON><PERSON><PERSON> mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC", "trading_order_description_gtc": "<PERSON><PERSON><PERSON> đượ<PERSON> kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập", "trading_order_command_description": "<PERSON><PERSON><PERSON> đượ<PERSON> kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt", "trading_take_profit_command_description": "<PERSON><PERSON><PERSON> bán đượ<PERSON> kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT", "traing_stop_loss_command_description": "<PERSON><PERSON><PERSON> bán đượ<PERSON> kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT", "trading_waiting_command": "<PERSON><PERSON><PERSON> chờ", "trading_stop_loss": "Cắt lỗ", "trading_take_profit": "<PERSON><PERSON><PERSON> lời", "trading_stock_code": "Stock Code", "trading_cost_price": "Cost Price", "trading_volume_title": "Volume", "trading_expected_profit_loss": "Expected P/L", "trading_market_value": "Market Value", "trading_capital_value": "Capital Value", "trading_stock_symbol": "Stock Symbol", "trading_total_stock_volume": "Total Stock Volume", "trading_average_cost_price": "Average Cost Price", "trading_capital_cost_price": "Average Cost Price", "trading_volume_holding": "Volume of holdings", "trading_investment_principal": "Investment Principal", "trading_available_stock_volume": "Available Stock Volume", "trading_pending_stock": "Pending Stock", "trading_pending_rights": "Pending Rights", "trading_restricted_stock": "Restricted Stock", "trading_blocked_stock": "Blocked Stock", "trading_collateral_stock": "Collateral Stock", "trading_portfolio_weight": "Portfolio Weight", "trading_holding_portfolio": "Holding Portfolio", "trading_showMessageNextTime": "<PERSON><PERSON><PERSON> thị thông báo này trong lần sau", "trading_cancel_all_order": "Cancel all order", "trading_effective_time": "Effective time", "trading_activation_conditions": "Activation conditions", "trading_condition_dilution_choice": "When there is an event of stock price dilution rights", "trading_condition_take_profit_rate_percent": "Take Profit Ratio (%)", "trading_condition_profit_margin": "Profit margin", "trading_condition_stop_loss_rate_percent": "Stop loss ratio (%)", "trading_condition_stop_loss_margin": "Stop loss margin", "trading_condition_slippage_margin": "Slippage margin", "trading_order_price_invalid": "Order price invalid", "trading_order_take_profit": "Order take profit", "trading_order_stop_loss": "Order stop loss", "trading_conditional_order_popup_note": "The order can only be activated once during its validity period.", "derivative_open_position": "Open position", "derivative_activation_conditions": "Conditions", "derivative_active_price_must_be_less_than_price": "Buy command: Active price must be less than price", "derivative_active_price_must_be_greater_than_price": "Sell command: Active price must be greater than price", "derivative_unpaid_interest": "Unpaid interest", "derivative_average_price": "Average price: ", "derivative_maximum_weight": "KL tối đa: ", "derivative_close_position_success": "Close position success", "derivative_over_the_maximum_volume2": "Over the maximum volume", "derivative_order_success": "Order Success", "derivative_close_position": "Close position", "derivative_contract_code": "Contract Code", "derivative_order_type": "Order type", "derivative_mass": "Mass", "derivative_price_order": "Set price", "derivative_show_message_next_time": "Show this message next time", "derivative_cancel": "Cancel", "derivative_confirm": "Confirm", "derivative_regular_order": "Regular order", "derivative_stop_order_command": "Stop Order Command", "derivative_trailing_stop_command": "Trailing Stop Command", "derivative_stop_loss_or_take_profit_command": "Stop Loss/ Take Profit Command", "derivative_order_accept": "Order accept", "derivative_future": "Future", "derivative_contract": "Contract and terms", "derivative_terms_and_conditions": "Terms and conditions", "derivative_by_pressing_the_button": "By pressing the button", "derivative_complete": "Complete", "derivative_back": "Back", "derivative_safe": "Safe", "derivative_warning": "Warning", "derivative_handle": "<PERSON><PERSON>", "derivative_see_detail": "See Detail", "derivative_net_assets": "Net assets", "derivative_profit_loss_during_day": "Profit/loss the day", "derivative_tkps_usage_rate": "TSKQ usage rate", "derivative_vmamt_content": "Include both settled and unrealized P&L for the day", "derivative_sign_up_success": "Sign Up Success", "derivative_back_to_market_screen": "About the Market screen", "derivative_amount_be_added": "Amount to be added", "derivative_cash_value_needs_to_be_added": "Cash value needs to be added so that the KQ usage rate reaches the safe threshold.", "derivative_account_await_approve": "Derivative account is awaiting approval.\nThank you.", "derivative_we_will_notify_you_account": "We will notify you once your account is successfully activated. Normally, you can trade after 1 working day. Thank you.", "derivative_confirm_and_agree_terms": " ,you acknowledge that you have read, understood and agreed to the above contracts.", "trading_edit_cancel_title": "Edit/Cancel", "trading_cancel_all_orders_confirmation": "Are you sure you want to cancel all orders?", "derivativeDepositOrWithdrawDerivative": "Nộp/rút ký quỹ PS", "derivativeIncomeStatement": "Sao kê lãi lỗ", "derivativeTransactionHistory": "<PERSON><PERSON><PERSON> sử giao dịch", "derivativeSetupTPSLInCategory": "Cài đặt SL/TP trên danh mục", "derivativeSetupTPSLInCategoryNote": "<PERSON><PERSON><PERSON> năng cho phép cài đặt điều kiện Cắt lỗ/ Chốt lời (SL/TP) ngay trên màn danh mục vị thế mở. Quý khách vui lòng xem chi tiết lệnh con tại sổ lệnh thường hoặc sổ lệnh điều kiện sau khi lệnh được kích hoạt. Xem thêm nguyên tắc hoạt động của lệnh", "derivativeAtHere": " tại đây.", "derivativeContentNotYetAccountPopup": "<PERSON><PERSON><PERSON> khách chưa có tài khoản phái sinh. Mở tài khoản ngay để trải nghiệm giao dịch nhanh chóng cùng VPBankS.", "derivativeNotification": "<PERSON><PERSON><PERSON><PERSON> báo", "derivativeAccountPendingApprove": "<PERSON><PERSON><PERSON> khoản phái sinh đang chờ phê duyệt. <PERSON>u<PERSON> khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ", "derivativeAccountInvalid": "<PERSON><PERSON><PERSON> khoản phái sinh đang chờ phê duyệt. <PERSON>u<PERSON> khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ", "derivativeDialogCloseButton": "Đ<PERSON><PERSON>", "derivativeDialogRegisterButton": "<PERSON><PERSON><PERSON> ký"}