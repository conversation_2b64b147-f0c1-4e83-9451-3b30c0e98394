import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';

enum DerivativeOrderStatusEnum {
  all, // Tất cả
  pending, // Ch<PERSON> kích hoạt
  activated, // <PERSON><PERSON> kích hoạt
  cancelled, // <PERSON><PERSON> hủy
  rejected, // Từ chối
  expired, // Hết hi<PERSON>u l<PERSON>
}

extension DerivativeOrderStatusEnumExt on DerivativeOrderStatusEnum {
  String get title {
    switch (this) {
      case DerivativeOrderStatusEnum.all:
        return 'Tất cả';
      case DerivativeOrderStatusEnum.pending:
        return 'Chờ kích hoạt';
      case DerivativeOrderStatusEnum.activated:
        return 'Đã kích hoạt';
      case DerivativeOrderStatusEnum.cancelled:
        return 'Đã hủy';
      case DerivativeOrderStatusEnum.rejected:
        return 'Từ chối';
      case DerivativeOrderStatusEnum.expired:
        return 'Hết hiệu lực';
    }
  }

  Color get color {
    switch (this) {
      case DerivativeOrderStatusEnum.all:
        return vpColor.textPrimary;
      case DerivativeOrderStatusEnum.pending:
        return vpColor.textAccentBlue;
      case DerivativeOrderStatusEnum.activated:
        return vpColor.textAccentGreen;
      case DerivativeOrderStatusEnum.cancelled:
        return vpColor.textAccentRed;
      case DerivativeOrderStatusEnum.rejected:
        return vpColor.textAccentRed;
      case DerivativeOrderStatusEnum.expired:
        return vpColor.textAccentRed;
    }
  }

  Color get colorBackground {
    switch (this) {
      case DerivativeOrderStatusEnum.all:
        return vpColor.backgroundElevation1;
      case DerivativeOrderStatusEnum.pending:
        return vpColor.backgroundAccentBlue;
      case DerivativeOrderStatusEnum.activated:
        return vpColor.backgroundAccentGreen;
      case DerivativeOrderStatusEnum.cancelled:
        return vpColor.backgroundAccentRed;
      case DerivativeOrderStatusEnum.rejected:
        return vpColor.backgroundAccentRed;
      case DerivativeOrderStatusEnum.expired:
        return vpColor.backgroundAccentRed;
    }
  }

  static DerivativeOrderStatusEnum orderStatusFromString(String? code) {
    switch (code?.toUpperCase()) {
      case "I":
      case "N":
      case "P":
      case "W":
        return DerivativeOrderStatusEnum.pending;
      case "F":
      case "T":
      case "B":
        return DerivativeOrderStatusEnum.activated;
      case "C":
        return DerivativeOrderStatusEnum.cancelled;
      case "R":
        return DerivativeOrderStatusEnum.rejected;
      case "E":
        return DerivativeOrderStatusEnum.expired;

      default:
        return DerivativeOrderStatusEnum.expired;
    }
  }

  String get codeRequest {
    switch (this) {
      case DerivativeOrderStatusEnum.all:
        return "ALL";
      case DerivativeOrderStatusEnum.pending:
        return "I,N,P,W";
      case DerivativeOrderStatusEnum.activated:
        return "F,T,B";
      case DerivativeOrderStatusEnum.cancelled:
        return "C";
      case DerivativeOrderStatusEnum.rejected:
        return "R";
      case DerivativeOrderStatusEnum.expired:
        return "E";
    }
  }
}
