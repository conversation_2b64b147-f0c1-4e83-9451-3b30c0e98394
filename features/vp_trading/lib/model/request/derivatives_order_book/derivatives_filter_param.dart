import 'package:equatable/equatable.dart';
import 'package:vp_trading/model/enum/derivative/derivative_order_status_enum.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

/// Filter parameters for derivatives order book
class DerivativesFilterParam extends Equatable {
  final OrderTypeEnum? transactionType;
  final List<OrderStatusEnum>? orderStatus;
  final List<DerivativeOrderStatusEnum>? derivativeOrderStatus;
  final List<ConditionOrderTypeFuEnum>? conditionOrderTypes;

  const DerivativesFilterParam({
    this.transactionType,
    this.orderStatus,
    this.derivativeOrderStatus,
    this.conditionOrderTypes,
  });

  @override
  List<Object?> get props => [
    transactionType,
    orderStatus,
    derivativeOrderStatus,
    conditionOrderTypes,
  ];

  DerivativesFilterParam copyWith({
    OrderTypeEnum? transactionType,
    List<OrderStatusEnum>? orderStatus,
    List<DerivativeOrderStatusEnum>? derivativeOrderStatus,
    List<ConditionOrderTypeFuEnum>? conditionOrderTypes,
  }) {
    return DerivativesFilterParam(
      transactionType: transactionType ?? this.transactionType,
      orderStatus: orderStatus ?? this.orderStatus,
      derivativeOrderStatus:
          derivativeOrderStatus ?? this.derivativeOrderStatus,
      conditionOrderTypes: conditionOrderTypes ?? this.conditionOrderTypes,
    );
  }

  /// Check if filter is in default state (All selected for both criteria)
  bool get isDefault {
    final isTransactionTypeDefault =
        transactionType == null || transactionType == OrderTypeEnum.all;
    final isOrderStatusDefault =
        orderStatus == null ||
        orderStatus!.isEmpty ||
        orderStatus!.contains(OrderStatusEnum.all);
    final isDerivativeOrderStatusDefault =
        derivativeOrderStatus == null ||
        derivativeOrderStatus!.isEmpty ||
        derivativeOrderStatus!.contains(DerivativeOrderStatusEnum.all);
    final isConditionOrderTypesDefault =
        conditionOrderTypes == null ||
        conditionOrderTypes!.isEmpty ||
        conditionOrderTypes!.contains(ConditionOrderTypeFuEnum.all);

    return isTransactionTypeDefault &&
        isOrderStatusDefault &&
        isDerivativeOrderStatusDefault &&
        isConditionOrderTypesDefault;
  }

  /// Get the combined status codes for API request
  String get statusCodesForRequest {
    if (orderStatus == null ||
        orderStatus!.isEmpty ||
        orderStatus!.contains(OrderStatusEnum.all)) {
      return "";
    }

    final allCodes = <String>[];
    for (final status in orderStatus!) {
      if (status != OrderStatusEnum.all) {
        allCodes.addAll(status.codes);
      }
    }

    return allCodes.join(',');
  }

  /// Get the combined derivative status codes for API request
  String get derivativeStatusCodesForRequest {
    if (derivativeOrderStatus == null ||
        derivativeOrderStatus!.isEmpty ||
        derivativeOrderStatus!.contains(DerivativeOrderStatusEnum.all)) {
      return "";
    }

    final allCodes = <String>[];
    for (final status in derivativeOrderStatus!) {
      if (status != DerivativeOrderStatusEnum.all) {
        final codes = status.codeRequest.split(',');
        allCodes.addAll(codes);
      }
    }

    return allCodes.join(',');
  }

  /// Get transaction type code for API request
  String get transactionTypeCodeForRequest {
    if (transactionType == null || transactionType == OrderTypeEnum.all) {
      return "";
    }
    return transactionType!.codeRequest;
  }

  /// Get the combined condition order type codes for API request
  String get conditionOrderTypeCodesForRequest {
    if (conditionOrderTypes == null ||
        conditionOrderTypes!.isEmpty ||
        conditionOrderTypes!.contains(ConditionOrderTypeFuEnum.all)) {
      return "";
    }

    final allCodes = <String>[];
    for (final orderType in conditionOrderTypes!) {
      if (orderType != ConditionOrderTypeFuEnum.all) {
        allCodes.add(orderType.codeRequest);
      }
    }

    return allCodes.join(',');
  }

  /// Convert filter parameters to OrderBookRequest
  /// [useDerivativeStatus] determines whether to use derivative status or regular status
  OrderBookRequest toOrderBookRequest({
    String? accountId,
    bool useDerivativeStatus = false,
  }) {
    final statusCodes =
        useDerivativeStatus
            ? derivativeStatusCodesForRequest
            : statusCodesForRequest;

    return OrderBookRequest(
      accountId: accountId,
      orderStatus: statusCodes.isEmpty ? null : statusCodes,
      side:
          transactionTypeCodeForRequest.isEmpty
              ? null
              : transactionTypeCodeForRequest,
      orderTypes:
          conditionOrderTypeCodesForRequest.isEmpty
              ? null
              : conditionOrderTypeCodesForRequest,
    );
  }
}
