// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'condition_order_book_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConditionOrderBookModel _$ConditionOrderBookModelFromJson(
  Map<String, dynamic> json,
) => ConditionOrderBookModel(
  accountId: json['accountId'] as String?,
  activePrice: json['activePrice'] as num?,
  activepriceSL: json['activepriceSL'] as num?,
  activepriceTP: json['activepriceTP'] as num?,
  activeQty: json['activeQty'] as num?,
  activeTime: json['activeTime'] as String?,
  activeType: json['activeType'] as String?,
  additionalData: json['additionalData'] as Map<String, dynamic>?,
  allowAmend: json['allowAmend'] as String?,
  allowCancel: json['allowCancel'] as String?,
  cancelQty: json['cancelQty'] as num?,
  createBy: json['createBy'] as String?,
  createTime: json['createTime'] as String?,
  deltaType: json['deltaType'] as String?,
  deltaValue: json['deltaValue'] as num?,
  enErrorDesc: json['enErrorDesc'] as String?,
  enErrorDescDtl: json['enErrorDescDtl'] as String?,
  errorDesc: json['errorDesc'] as String?,
  errorDescDtl: json['errorDescDtl'] as String?,
  execAmt: json['execAmt'] as num?,
  execPrice: json['execPrice'] as num?,
  execQty: json['execQty'] as num?,
  feeAmt: json['feeAmt'] as String?,
  feedbackMsg: json['feedbackMsg'] as String?,
  feeRate: json['feeRate'] as String?,
  fromDate: json['fromDate'] as String?,
  fx1Price: json['fx1Price'] as num?,
  lastChange: json['lastChange'] as String?,
  openPrice: json['openPrice'] as num?,
  orderId: json['orderId'] as String?,
  orderStatus: json['orderStatus'] as String?,
  orderType: json['orderType'] as String?,
  originOrderId: json['originOrderId'] as String?,
  price: json['price'] as String?,
  priceSL: json['priceSL'] as String?,
  priceStep: json['priceStep'] as String?,
  priceTP: json['priceTP'] as String?,
  priceType: json['priceType'] as String?,
  priceTypeSL: json['priceTypeSL'] as String?,
  priceTypeTP: json['priceTypeTP'] as String?,
  qty: json['qty'] as num?,
  remainQty: json['remainQty'] as num?,
  serviceName: json['serviceName'] as String?,
  side: json['side'] as String?,
  split: json['split'] as String?,
  stopLosses: json['stopLosses'] as num?,
  stopLosses2: json['stopLosses2'] as num?,
  stopPrice: json['stopPrice'] as String?,
  stopPrice2: json['stopPrice2'] as num?,
  symbol: json['symbol'] as String?,
  taxAmt: json['taxAmt'] as String?,
  timeTypeValue: json['timeTypeValue'] as String?,
  toDate: json['toDate'] as String?,
  tradeDate: json['tradeDate'] as String?,
  tradeTime: json['tradeTime'] as String?,
  transId: json['transId'] as String?,
  diluationAction: json['diluationAction'] as String?,
  slipPagePrice: json['slipPagePrice'] as num?,
  via: json['via'] as String?,
);

Map<String, dynamic> _$ConditionOrderBookModelToJson(
  ConditionOrderBookModel instance,
) => <String, dynamic>{
  'accountId': instance.accountId,
  'activePrice': instance.activePrice,
  'activepriceSL': instance.activepriceSL,
  'activepriceTP': instance.activepriceTP,
  'activeQty': instance.activeQty,
  'activeTime': instance.activeTime,
  'activeType': instance.activeType,
  'additionalData': instance.additionalData,
  'allowAmend': instance.allowAmend,
  'allowCancel': instance.allowCancel,
  'cancelQty': instance.cancelQty,
  'createBy': instance.createBy,
  'createTime': instance.createTime,
  'deltaType': instance.deltaType,
  'deltaValue': instance.deltaValue,
  'enErrorDesc': instance.enErrorDesc,
  'enErrorDescDtl': instance.enErrorDescDtl,
  'errorDesc': instance.errorDesc,
  'errorDescDtl': instance.errorDescDtl,
  'execAmt': instance.execAmt,
  'execPrice': instance.execPrice,
  'execQty': instance.execQty,
  'feeAmt': instance.feeAmt,
  'feedbackMsg': instance.feedbackMsg,
  'feeRate': instance.feeRate,
  'fromDate': instance.fromDate,
  'fx1Price': instance.fx1Price,
  'lastChange': instance.lastChange,
  'openPrice': instance.openPrice,
  'orderId': instance.orderId,
  'orderStatus': instance.orderStatus,
  'orderType': instance.orderType,
  'originOrderId': instance.originOrderId,
  'price': instance.price,
  'priceSL': instance.priceSL,
  'priceStep': instance.priceStep,
  'priceTP': instance.priceTP,
  'priceType': instance.priceType,
  'priceTypeSL': instance.priceTypeSL,
  'priceTypeTP': instance.priceTypeTP,
  'qty': instance.qty,
  'remainQty': instance.remainQty,
  'serviceName': instance.serviceName,
  'side': instance.side,
  'split': instance.split,
  'stopLosses': instance.stopLosses,
  'stopLosses2': instance.stopLosses2,
  'stopPrice': instance.stopPrice,
  'stopPrice2': instance.stopPrice2,
  'symbol': instance.symbol,
  'taxAmt': instance.taxAmt,
  'timeTypeValue': instance.timeTypeValue,
  'toDate': instance.toDate,
  'tradeDate': instance.tradeDate,
  'tradeTime': instance.tradeTime,
  'transId': instance.transId,
  'diluationAction': instance.diluationAction,
  'slipPagePrice': instance.slipPagePrice,
  'via': instance.via,
};
