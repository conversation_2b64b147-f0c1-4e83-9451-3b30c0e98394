import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/model/enum/derivative/derivative_order_status_enum.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

part 'derivatives_order_book_filter_state.dart';

class DerivativesOrderBookFilterCubit
    extends Cubit<DerivativesOrderBookFilterState> {
  DerivativesOrderBookFilterCubit()
    : super(const DerivativesOrderBookFilterState());

  void init() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
      derivativeOrderStatus: [DerivativeOrderStatusEnum.all],
      conditionOrderTypes: [ConditionOrderTypeFuEnum.all],
    );

    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final defaultRegularRequest = defaultFilter.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
      useDerivativeStatus: false,
    );

    final defaultConditionalRequest = defaultFilter.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
      useDerivativeStatus: true,
    );

    emit(
      state.copyWith(
        regularOrderFilter: defaultFilter,
        conditionalOrderFilter: defaultFilter,
        regularOrderRequest: defaultRegularRequest,
        conditionalOrderRequest: defaultConditionalRequest,
      ),
    );
  }

  void changeTab(int tabIndex) {
    emit(state.copyWith(currentTabIndex: tabIndex));
  }

  void updateRegularOrderFilter(DerivativesFilterParam filterParam) {
    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final request = filterParam.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
      useDerivativeStatus: false,
    );

    emit(
      state.copyWith(
        regularOrderFilter: filterParam,
        regularOrderRequest: request,
      ),
    );
  }

  void updateConditionalOrderFilter(DerivativesFilterParam filterParam) {
    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final request = filterParam.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
      useDerivativeStatus: true,
    );

    emit(
      state.copyWith(
        conditionalOrderFilter: filterParam,
        conditionalOrderRequest: request,
      ),
    );
  }

  void updateCurrentFilter(DerivativesFilterParam filterParam) {
    switch (state.currentTabIndex) {
      case 0:
        updateRegularOrderFilter(filterParam);
        break;
      case 1:
        updateConditionalOrderFilter(filterParam);
        break;
    }
  }

  void resetCurrentFilter() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
      conditionOrderTypes: [ConditionOrderTypeFuEnum.all],
    );
    updateCurrentFilter(defaultFilter);
  }

  void resetAllFilters() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
      derivativeOrderStatus: [DerivativeOrderStatusEnum.all],
      conditionOrderTypes: [ConditionOrderTypeFuEnum.all],
    );

    final subAccountCubit = GetIt.instance<SubAccountCubit>();
    final currentAccount =
        subAccountCubit.state.derivativeSubAccount.firstOrNull;

    final defaultRegularRequest = defaultFilter.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
      useDerivativeStatus: false,
    );

    final defaultConditionalRequest = defaultFilter.toOrderBookRequest(
      accountId: currentAccount?.id ?? "",
      useDerivativeStatus: true,
    );

    emit(
      state.copyWith(
        regularOrderFilter: defaultFilter,
        conditionalOrderFilter: defaultFilter,
        regularOrderRequest: defaultRegularRequest,
        conditionalOrderRequest: defaultConditionalRequest,
      ),
    );
  }
}
