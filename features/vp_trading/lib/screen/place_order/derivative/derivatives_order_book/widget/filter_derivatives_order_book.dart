import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/derivative/derivative_order_status_enum.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

void openFilterDerivativesOrderBottomSheet(
  BuildContext context, {
  DerivativesFilterParam? initialFilter,
  Function(DerivativesFilterParam)? onApply,
  int currentTabIndex = 0,
}) {
  VPPopup.bottomSheet(
    _FilterDerivativesOrder(
      initialFilter: initialFilter,
      onApply: onApply,
      currentTabIndex: currentTabIndex,
    ),
  ).showSheet(context);
}

class _FilterDerivativesOrder extends StatefulWidget {
  const _FilterDerivativesOrder({
    this.initialFilter,
    this.onApply,
    this.currentTabIndex = 0,
  });

  final DerivativesFilterParam? initialFilter;
  final Function(DerivativesFilterParam)? onApply;
  final int currentTabIndex;

  @override
  State<_FilterDerivativesOrder> createState() =>
      __FilterDerivativesOrderState();
}

class __FilterDerivativesOrderState extends State<_FilterDerivativesOrder> {
  late OrderTypeEnum _selectedTransactionType;
  late List<OrderStatusEnum> _selectedOrderStatus;
  late List<DerivativeOrderStatusEnum> _selectedDerivativeOrderStatus;
  late List<ConditionOrderTypeFuEnum> _selectedConditionOrderTypes;

  @override
  void initState() {
    super.initState();

    // Initialize transaction type
    _selectedTransactionType =
        widget.initialFilter?.transactionType ?? OrderTypeEnum.all;

    // Initialize order status selection
    if (widget.initialFilter?.orderStatus == null ||
        widget.initialFilter!.orderStatus!.isEmpty ||
        widget.initialFilter!.orderStatus!.contains(OrderStatusEnum.all)) {
      _selectedOrderStatus = [OrderStatusEnum.all];
    } else {
      _selectedOrderStatus = List<OrderStatusEnum>.from(
        widget.initialFilter!.orderStatus!,
      );
    }

    // Initialize derivative order status selection
    if (widget.initialFilter?.derivativeOrderStatus == null ||
        widget.initialFilter!.derivativeOrderStatus!.isEmpty ||
        widget.initialFilter!.derivativeOrderStatus!.contains(
          DerivativeOrderStatusEnum.all,
        )) {
      _selectedDerivativeOrderStatus = [DerivativeOrderStatusEnum.all];
    } else {
      _selectedDerivativeOrderStatus = List<DerivativeOrderStatusEnum>.from(
        widget.initialFilter!.derivativeOrderStatus!,
      );
    }

    // Initialize condition order types selection
    if (widget.initialFilter?.conditionOrderTypes == null ||
        widget.initialFilter!.conditionOrderTypes!.isEmpty ||
        widget.initialFilter!.conditionOrderTypes!.contains(
          ConditionOrderTypeFuEnum.all,
        )) {
      _selectedConditionOrderTypes = [ConditionOrderTypeFuEnum.all];
    } else {
      _selectedConditionOrderTypes = List<ConditionOrderTypeFuEnum>.from(
        widget.initialFilter!.conditionOrderTypes!,
      );
    }
  }

  void _onSelectTransactionType(OrderTypeEnum transactionType) {
    setState(() {
      _selectedTransactionType = transactionType;
    });
  }

  bool get isSelectAllOrderStatus =>
      _selectedOrderStatus.contains(OrderStatusEnum.all) ||
      _selectedOrderStatus.length == OrderStatusEnum.values.length;

  void _onTapAllOrderStatus() {
    setState(() {
      _selectedOrderStatus = [OrderStatusEnum.all];
    });
  }

  void _onTapOrderStatus(OrderStatusEnum status) {
    setState(() {
      if (_selectedOrderStatus.contains(OrderStatusEnum.all)) {
        // If "all" is selected, remove "all" and add all other statuses except the clicked one
        _selectedOrderStatus =
            OrderStatusEnum.values
                .where((e) => e != OrderStatusEnum.all && e != status)
                .toList();
      } else {
        if (_selectedOrderStatus.contains(status)) {
          // Prevent unchecking the last item if it's not "all"
          if (_selectedOrderStatus.length == 1 &&
              !_selectedOrderStatus.contains(OrderStatusEnum.all)) {
            return; // Don't allow unchecking the last item
          }
          _selectedOrderStatus.remove(status);
        } else {
          _selectedOrderStatus.add(status);
        }
      }
      // If nothing is selected, default to 'all'
      if (_selectedOrderStatus.isEmpty) {
        _selectedOrderStatus = [OrderStatusEnum.all];
      }
      // If all statuses are selected (excluding 'all'), switch to 'all'
      if (_selectedOrderStatus.length == OrderStatusEnum.values.length - 1) {
        _selectedOrderStatus = [OrderStatusEnum.all];
      }
    });
  }

  bool get isSelectAllDerivativeOrderStatus =>
      _selectedDerivativeOrderStatus.contains(DerivativeOrderStatusEnum.all) ||
      _selectedDerivativeOrderStatus.length ==
          DerivativeOrderStatusEnum.values.length;

  void _onTapAllDerivativeOrderStatus() {
    setState(() {
      _selectedDerivativeOrderStatus = [DerivativeOrderStatusEnum.all];
    });
  }

  void _onTapDerivativeOrderStatus(DerivativeOrderStatusEnum status) {
    setState(() {
      if (_selectedDerivativeOrderStatus.contains(
        DerivativeOrderStatusEnum.all,
      )) {
       
        _selectedDerivativeOrderStatus =
            DerivativeOrderStatusEnum.values
                .where((e) => e != DerivativeOrderStatusEnum.all && e != status)
                .toList();
      } else {
        if (_selectedDerivativeOrderStatus.contains(status)) {
          // Prevent unchecking the last item if it's not "all"
          if (_selectedDerivativeOrderStatus.length == 1 &&
              !_selectedDerivativeOrderStatus.contains(
                DerivativeOrderStatusEnum.all,
              )) {
            return; // Don't allow unchecking the last item
          }
          _selectedDerivativeOrderStatus.remove(status);
        } else {
          _selectedDerivativeOrderStatus.add(status);
        }
      }
      // If nothing is selected, default to 'all'
      if (_selectedDerivativeOrderStatus.isEmpty) {
        _selectedDerivativeOrderStatus = [DerivativeOrderStatusEnum.all];
      }
      // If all statuses are selected (excluding 'all'), switch to 'all'
      if (_selectedDerivativeOrderStatus.length ==
          DerivativeOrderStatusEnum.values.length - 1) {
        _selectedDerivativeOrderStatus = [DerivativeOrderStatusEnum.all];
      }
    });
  }

  bool get isSelectAllConditionOrderTypes =>
      _selectedConditionOrderTypes.contains(ConditionOrderTypeFuEnum.all) ||
      _selectedConditionOrderTypes.length ==
          ConditionOrderTypeFuEnum.values.length;

  void _onTapAllConditionOrderTypes() {
    setState(() {
      _selectedConditionOrderTypes = [ConditionOrderTypeFuEnum.all];
    });
  }

  void _onTapConditionOrderType(ConditionOrderTypeFuEnum orderType) {
    setState(() {
      if (_selectedConditionOrderTypes.contains(ConditionOrderTypeFuEnum.all)) {
        // If "all" is selected, remove "all" and add all other types except the clicked one
        _selectedConditionOrderTypes =
            ConditionOrderTypeFuEnum.values
                .where(
                  (e) => e != ConditionOrderTypeFuEnum.all && e != orderType,
                )
                .toList();
      } else {
        if (_selectedConditionOrderTypes.contains(orderType)) {
          // Prevent unchecking the last item if it's not "all"
          if (_selectedConditionOrderTypes.length == 1 &&
              !_selectedConditionOrderTypes.contains(
                ConditionOrderTypeFuEnum.all,
              )) {
            return; // Don't allow unchecking the last item
          }
          _selectedConditionOrderTypes.remove(orderType);
        } else {
          _selectedConditionOrderTypes.add(orderType);
        }
      }
      // If nothing is selected, default to 'all'
      if (_selectedConditionOrderTypes.isEmpty) {
        _selectedConditionOrderTypes = [ConditionOrderTypeFuEnum.all];
      }
      // If all types are selected (excluding 'all'), switch to 'all'
      if (_selectedConditionOrderTypes.length ==
          ConditionOrderTypeFuEnum.values.length - 1) {
        _selectedConditionOrderTypes = [ConditionOrderTypeFuEnum.all];
      }
    });
  }

  void _onReset() {
    setState(() {
      _selectedTransactionType = OrderTypeEnum.all;
      _selectedOrderStatus = [OrderStatusEnum.all];
      _selectedDerivativeOrderStatus = [DerivativeOrderStatusEnum.all];
      _selectedConditionOrderTypes = [ConditionOrderTypeFuEnum.all];
    });
  }

  void _onApply() {
    if (widget.onApply != null) {
      List<OrderStatusEnum> statusToReturn;
      if (_selectedOrderStatus.contains(OrderStatusEnum.all)) {
        statusToReturn = [OrderStatusEnum.all];
      } else {
        statusToReturn = List<OrderStatusEnum>.from(_selectedOrderStatus);
      }

      List<DerivativeOrderStatusEnum> derivativeStatusToReturn;
      if (_selectedDerivativeOrderStatus.contains(
        DerivativeOrderStatusEnum.all,
      )) {
        derivativeStatusToReturn = [DerivativeOrderStatusEnum.all];
      } else {
        derivativeStatusToReturn = List<DerivativeOrderStatusEnum>.from(
          _selectedDerivativeOrderStatus,
        );
      }

      List<ConditionOrderTypeFuEnum> conditionOrderTypesToReturn;
      if (_selectedConditionOrderTypes.contains(ConditionOrderTypeFuEnum.all)) {
        conditionOrderTypesToReturn = [ConditionOrderTypeFuEnum.all];
      } else {
        conditionOrderTypesToReturn = List<ConditionOrderTypeFuEnum>.from(
          _selectedConditionOrderTypes,
        );
      }

      final filterParam = DerivativesFilterParam(
        transactionType: _selectedTransactionType,
        orderStatus: statusToReturn,
        derivativeOrderStatus: derivativeStatusToReturn,
        conditionOrderTypes: conditionOrderTypesToReturn,
      );

      widget.onApply!(filterParam);
    }
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: SizedBox(
        width: MediaQuery.sizeOf(context).width,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              VPTradingLocalize.current.trading_transaction_type,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 16,
              children: [
                ...OrderTypeEnum.values.map(
                  (e) => VPChipView.dynamic(
                    text: e.titleOrderDerivative,
                    size: ChipSize.medium,
                    onTap: () => _onSelectTransactionType(e),
                    style:
                        _selectedTransactionType == e
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              VPTradingLocalize.current.trading_status_filter_title,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
            const SizedBox(height: 8),
            // Conditional Status filter based on tab
            if (widget.currentTabIndex == 0) ...[
              // Regular Orders Tab - Use OrderStatusEnum
              Wrap(
                spacing: 8,
                runSpacing: 16,
                children: [
                  VPChipView.dynamic(
                    text: VPTradingLocalize.current.trading_all,
                    size: ChipSize.medium,
                    onTap: _onTapAllOrderStatus,
                    style:
                        isSelectAllOrderStatus
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                  ...OrderStatusEnum.values
                      .where((e) => e != OrderStatusEnum.all)
                      .map(
                        (e) => VPChipView.dynamic(
                          text: e.title,
                          size: ChipSize.medium,
                          onTap: () => _onTapOrderStatus(e),
                          style:
                              isSelectAllOrderStatus
                                  ? ChipStyle.selected
                                  : _selectedOrderStatus.contains(e)
                                  ? ChipStyle.selected
                                  : ChipStyle.chipDefault,
                        ),
                      ),
                ],
              ),
            ] else ...[
              // Conditional Orders Tab - Use DerivativeOrderStatusEnum
              Wrap(
                spacing: 8,
                runSpacing: 16,
                children: [
                  VPChipView.dynamic(
                    text: VPTradingLocalize.current.trading_all,
                    size: ChipSize.medium,
                    onTap: _onTapAllDerivativeOrderStatus,
                    style:
                        isSelectAllDerivativeOrderStatus
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                  ...DerivativeOrderStatusEnum.values
                      .where((e) => e != DerivativeOrderStatusEnum.all)
                      .map(
                        (e) => VPChipView.dynamic(
                          text: e.title,
                          size: ChipSize.medium,
                          onTap: () => _onTapDerivativeOrderStatus(e),
                          style:
                              isSelectAllDerivativeOrderStatus
                                  ? ChipStyle.selected
                                  : _selectedDerivativeOrderStatus.contains(e)
                                  ? ChipStyle.selected
                                  : ChipStyle.chipDefault,
                        ),
                      ),
                ],
              ),
            ],
            // Condition Order Types filter - only show on conditional orders tab
            if (widget.currentTabIndex == 1) ...[
              const SizedBox(height: 24),
              Text(
                VPTradingLocalize.current.trading_order_type_filter,
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 16,
                children: [
                  VPChipView.dynamic(
                    text: VPTradingLocalize.current.trading_all,
                    size: ChipSize.medium,
                    onTap: _onTapAllConditionOrderTypes,
                    style:
                        isSelectAllConditionOrderTypes
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                  ...ConditionOrderTypeFuEnum.values
                      .where((e) => e != ConditionOrderTypeFuEnum.all)
                      .map(
                        (e) => VPChipView.dynamic(
                          text: e.title,
                          size: ChipSize.medium,
                          onTap: () => _onTapConditionOrderType(e),
                          style:
                              isSelectAllConditionOrderTypes
                                  ? ChipStyle.selected
                                  : _selectedConditionOrderTypes.contains(e)
                                  ? ChipStyle.selected
                                  : ChipStyle.chipDefault,
                        ),
                      ),
                ],
              ),
            ],
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: VpsButton.secondaryXsSmall(
                    title: VPTradingLocalize.current.trading_reset,
                    onPressed: _onReset,
                    alignment: Alignment.center,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: VpsButton.primaryXsSmall(
                    title: VPTradingLocalize.current.trading_apply,
                    onPressed: _onApply,
                    alignment: Alignment.center,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
