import 'package:flutter/material.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/model/enum/derivative/derivative_order_status_enum.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';
import 'package:vp_trading/widgets/status_order_widget.dart';

class ConditionalOrderItem extends StatefulWidget {
  final ConditionOrderBookModel item;
  final bool isSelected;
  final bool isMultiSelectMode;
  final VoidCallback onDelete;
  final VoidCallback onEdit;
  final ValueChanged<ConditionOrderBookModel>? onSelectionChanged;

  const ConditionalOrderItem({
    super.key,
    required this.item,
    required this.isSelected,
    required this.isMultiSelectMode,
    required this.onDelete,
    required this.onEdit,
    this.onSelectionChanged,
  });

  @override
  State<ConditionalOrderItem> createState() => _ConditionalOrderItemState();
}

class _ConditionalOrderItemState extends State<ConditionalOrderItem>
    with SingleTickerProviderStateMixin {
  bool isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      isExpanded = !isExpanded;
    });

    if (isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation1,
        borderRadius: BorderRadius.circular(16),
        border:
            widget.isSelected
                ? Border.all(color: vpColor.textAccentBlue, width: 2)
                : null,
      ),
      child: InkWell(
        onTap:
            widget.isMultiSelectMode
                ? () => widget.onSelectionChanged?.call(widget.item)
                : _toggleExpanded,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: _buildMainContent(),
            ),
            SizeTransition(
              sizeFactor: _expandAnimation,
              child:
                  !widget.isMultiSelectMode
                      ? Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          bottom: 16,
                        ),
                        child: _buildOrderDetails(),
                      )
                      : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Row(
      children: [
        if (widget.isMultiSelectMode) ...[
          _buildCheckbox(),
          const SizedBox(width: 8),
        ],
        _buildSideIndicator(context),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.item.symbol ?? '-',
              style: context.textStyle.body14?.copyWith(
                fontWeight: FontWeight.w600,
                color: vpColor.textPrimary,
              ),
            ),
            const SizedBox(height: 2),
            _buildStatusTag(context),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${widget.item.qty ?? '-'}/${widget.item.execQty ?? '-'}',
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                FormatUtils.formatNumberMaxTwoDecimals(
                      double.tryParse(widget.item.price ?? '0'),
                    ) ??
                    '-',

                style: context.textStyle.captionRegular?.copyWith(
                  color: vpColor.textSecondary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 24),
        _buildActionButtons(context),
      ],
    );
  }

  Widget _buildCheckbox() {
    return Checkbox(
      value: widget.isSelected,
      onChanged: (_) => widget.onSelectionChanged?.call(widget.item),
      activeColor: vpColor.textAccentBlue,
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildActionButton(
          onTap: widget.item.isEnableEdit ? widget.onEdit : () {},
          icon: VpTradingAssets.icons.icEdit.svg(
            colorFilter:
                widget.item.isEnableEdit
                    ? null
                    : ColorFilter.mode(vpColor.iconDisabled, BlendMode.srcIn),
          ),
          enabled: widget.item.isEnableEdit,
        ),
        const SizedBox(width: 8),
        SizedBox(
          height: 24,
          child: VerticalDivider(width: 1, color: vpColor.textDisabled),
        ),
        const SizedBox(width: 8),
        _buildActionButton(
          onTap: widget.item.isEnableCancel ? widget.onDelete : () {},
          icon: VpTradingAssets.icons.icRemove2.svg(
            colorFilter:
                widget.item.isEnableCancel
                    ? null
                    : ColorFilter.mode(vpColor.iconDisabled, BlendMode.srcIn),
          ),
          enabled: widget.item.isEnableCancel,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required VoidCallback onTap,
    required Widget icon,
    bool enabled = true,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color:
              enabled
                  ? vpColor.backgroundElevation1
                  : vpColor.backgroundElevation0,
          shape: BoxShape.circle,
          border: Border.all(color: vpColor.textDisabled, width: 0.5),
        ),
        padding: const EdgeInsets.all(8),
        child: icon,
      ),
    );
  }

  Widget _buildOrderDetails() {
    return Column(
      children: [
        _buildDetailRow(
          'Loại lệnh',
          widget.item.conditionOrderTypeFuEnum.title,
        ),
        const SizedBox(height: 8),
        _buildDetailRow('Thời gian đặt', widget.item.createTime ?? '-'),
        const SizedBox(height: 8),
        _buildDetailRow('Trạng gian kích hoạt', widget.item.activeTime ?? '-'),
        const SizedBox(height: 8),
        _buildDetailRow('Số hiệu lệnh gốc', widget.item.orderId ?? '-'),
        const SizedBox(height: 8),
        _buildDetailRow(
          'Giá chốt lời',
          FormatUtils.formatNumberMaxTwoDecimals(
                double.tryParse(widget.item.priceTP ?? '0'),
              ) ??
              '-',
        ),
        const SizedBox(height: 8),
        _buildDetailRow(
          'Gia cắt lỗ',
          FormatUtils.formatNumberMaxTwoDecimals(
                double.tryParse(widget.item.priceSL ?? '0'),
              ) ??
              '-',
        ),
        const SizedBox(height: 8),
        _buildDetailRow(
          'Giá kích hoạt cắt lỗ',
          _buildActivationPriceDisplay(widget.item.activepriceSL),
        ),
        const SizedBox(height: 8),
        _buildDetailRow('Kênh đặt', widget.item.viaName),
        const SizedBox(height: 8),
        _buildDetailRow(
          'Lệnh con',
          '-',
          valueWidget: GestureDetector(
            onTap: () {},
            child: Text(
              "Xem chi tiết",
              style: vpTextStyle.body14?.copyWith(color: vpColor.textBrand),
            ),
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Widget? valueWidget}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textStyle.body14?.copyWith(
            color: vpColor.textSecondary,
          ),
        ),
        valueWidget ??
            Text(
              value,
              style: context.textStyle.body14?.copyWith(
                color: vpColor.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
      ],
    );
  }

  _buildSideIndicator(BuildContext context) {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: _getSideColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Center(
        child: Text(
          _getSideText(),
          style: context.textStyle.subtitle14?.copyWith(color: _getSideColor()),
        ),
      ),
    );
  }

  String _getSideText() {
    return widget.item.orderTypeFUEnum == OrderTypeEnum.buy ? 'L' : 'S';
  }

  Color _getSideColor() {
    return widget.item.orderTypeFUEnum == OrderTypeEnum.buy
        ? vpColor.textAccentGreen
        : vpColor.textAccentRed;
  }

  Widget _buildStatusTag(BuildContext context) {
    return StatusOrderNewWidget(
      textStatus: widget.item.derivativeOrderStatusEnum.title,
      colorStatus: widget.item.derivativeOrderStatusEnum.colorBackground,
      colorText: widget.item.derivativeOrderStatusEnum.color,
    );
  }

  String _buildActivationPriceDisplay(num? activePriceSL) {
    if (activePriceSL == null) return '-';
    final formattedPrice =
        FormatUtils.formatNumberMaxTwoDecimals(activePriceSL) ?? '-';
    return widget.item.orderTypeFUEnum == OrderTypeEnum.buy
        ? '≤ $formattedPrice'
        : '≥ $formattedPrice';
  }
}
