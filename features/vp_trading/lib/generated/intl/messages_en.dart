// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "derivativeAccountInvalid": MessageLookupByLibrary.simpleMessage(
      "Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ",
    ),
    "derivativeAccountPendingApprove": MessageLookupByLibrary.simpleMessage(
      "Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ",
    ),
    "derivativeAtHere": MessageLookupByLibrary.simpleMessage(" tại đây."),
    "derivativeContentNotYetAccountPopup": MessageLookupByLibrary.simpleMessage(
      "Quý khách chưa có tài khoản phái sinh. Mở tài khoản ngay để trải nghiệm giao dịch nhanh chóng cùng VPBankS.",
    ),
    "derivativeDepositOrWithdrawDerivative":
        MessageLookupByLibrary.simpleMessage("Nộp/rút ký quỹ PS"),
    "derivativeDialogCloseButton": MessageLookupByLibrary.simpleMessage("Đóng"),
    "derivativeDialogRegisterButton": MessageLookupByLibrary.simpleMessage(
      "Đăng ký",
    ),
    "derivativeIncomeStatement": MessageLookupByLibrary.simpleMessage(
      "Sao kê lãi lỗ",
    ),
    "derivativeNotification": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "derivativeSetupTPSLInCategory": MessageLookupByLibrary.simpleMessage(
      "Cài đặt SL/TP trên danh mục",
    ),
    "derivativeSetupTPSLInCategoryNote": MessageLookupByLibrary.simpleMessage(
      "Tính năng cho phép cài đặt điều kiện Cắt lỗ/ Chốt lời (SL/TP) ngay trên màn danh mục vị thế mở. Quý khách vui lòng xem chi tiết lệnh con tại sổ lệnh thường hoặc sổ lệnh điều kiện sau khi lệnh được kích hoạt. Xem thêm nguyên tắc hoạt động của lệnh",
    ),
    "derivativeTransactionHistory": MessageLookupByLibrary.simpleMessage(
      "Lịch sử giao dịch",
    ),
    "derivative_account_await_approve": MessageLookupByLibrary.simpleMessage(
      "Derivative account is awaiting approval.\nThank you.",
    ),
    "derivative_activation_conditions": MessageLookupByLibrary.simpleMessage(
      "Conditions",
    ),
    "derivative_active_price_must_be_greater_than_price":
        MessageLookupByLibrary.simpleMessage(
          "Sell command: Active price must be greater than price",
        ),
    "derivative_active_price_must_be_less_than_price":
        MessageLookupByLibrary.simpleMessage(
          "Buy command: Active price must be less than price",
        ),
    "derivative_amount_be_added": MessageLookupByLibrary.simpleMessage(
      "Amount to be added",
    ),
    "derivative_average_price": MessageLookupByLibrary.simpleMessage(
      "Average price: ",
    ),
    "derivative_back": MessageLookupByLibrary.simpleMessage("Back"),
    "derivative_back_to_market_screen": MessageLookupByLibrary.simpleMessage(
      "About the Market screen",
    ),
    "derivative_by_pressing_the_button": MessageLookupByLibrary.simpleMessage(
      "By pressing the button",
    ),
    "derivative_cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "derivative_cash_value_needs_to_be_added": MessageLookupByLibrary.simpleMessage(
      "Cash value needs to be added so that the KQ usage rate reaches the safe threshold.",
    ),
    "derivative_close_position": MessageLookupByLibrary.simpleMessage(
      "Close position",
    ),
    "derivative_close_position_success": MessageLookupByLibrary.simpleMessage(
      "Close position success",
    ),
    "derivative_complete": MessageLookupByLibrary.simpleMessage("Complete"),
    "derivative_confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "derivative_confirm_and_agree_terms": MessageLookupByLibrary.simpleMessage(
      " ,you acknowledge that you have read, understood and agreed to the above contracts.",
    ),
    "derivative_contract": MessageLookupByLibrary.simpleMessage(
      "Contract and terms",
    ),
    "derivative_contract_code": MessageLookupByLibrary.simpleMessage(
      "Contract Code",
    ),
    "derivative_future": MessageLookupByLibrary.simpleMessage("Future"),
    "derivative_handle": MessageLookupByLibrary.simpleMessage("Handle"),
    "derivative_mass": MessageLookupByLibrary.simpleMessage("Mass"),
    "derivative_maximum_weight": MessageLookupByLibrary.simpleMessage(
      "KL tối đa: ",
    ),
    "derivative_net_assets": MessageLookupByLibrary.simpleMessage("Net assets"),
    "derivative_open_position": MessageLookupByLibrary.simpleMessage(
      "Open position",
    ),
    "derivative_order_accept": MessageLookupByLibrary.simpleMessage(
      "Order accept",
    ),
    "derivative_order_success": MessageLookupByLibrary.simpleMessage(
      "Order Success",
    ),
    "derivative_order_type": MessageLookupByLibrary.simpleMessage("Order type"),
    "derivative_over_the_maximum_volume2": MessageLookupByLibrary.simpleMessage(
      "Over the maximum volume",
    ),
    "derivative_price_order": MessageLookupByLibrary.simpleMessage("Set price"),
    "derivative_profit_loss_during_day": MessageLookupByLibrary.simpleMessage(
      "Profit/loss the day",
    ),
    "derivative_regular_order": MessageLookupByLibrary.simpleMessage(
      "Regular order",
    ),
    "derivative_safe": MessageLookupByLibrary.simpleMessage("Safe"),
    "derivative_see_detail": MessageLookupByLibrary.simpleMessage("See Detail"),
    "derivative_show_message_next_time": MessageLookupByLibrary.simpleMessage(
      "Show this message next time",
    ),
    "derivative_sign_up_success": MessageLookupByLibrary.simpleMessage(
      "Sign Up Success",
    ),
    "derivative_stop_loss_or_take_profit_command":
        MessageLookupByLibrary.simpleMessage("Stop Loss/ Take Profit Command"),
    "derivative_stop_order_command": MessageLookupByLibrary.simpleMessage(
      "Stop Order Command",
    ),
    "derivative_terms_and_conditions": MessageLookupByLibrary.simpleMessage(
      "Terms and conditions",
    ),
    "derivative_tkps_usage_rate": MessageLookupByLibrary.simpleMessage(
      "TSKQ usage rate",
    ),
    "derivative_trailing_stop_command": MessageLookupByLibrary.simpleMessage(
      "Trailing Stop Command",
    ),
    "derivative_unpaid_interest": MessageLookupByLibrary.simpleMessage(
      "Unpaid interest",
    ),
    "derivative_vmamt_content": MessageLookupByLibrary.simpleMessage(
      "Include both settled and unrealized P&L for the day",
    ),
    "derivative_warning": MessageLookupByLibrary.simpleMessage("Warning"),
    "derivative_we_will_notify_you_account": MessageLookupByLibrary.simpleMessage(
      "We will notify you once your account is successfully activated. Normally, you can trade after 1 working day. Thank you.",
    ),
    "trading_activation_conditions": MessageLookupByLibrary.simpleMessage(
      "Activation conditions",
    ),
    "trading_all": MessageLookupByLibrary.simpleMessage("All"),
    "trading_apply": MessageLookupByLibrary.simpleMessage("Apply"),
    "trading_available_stock_volume": MessageLookupByLibrary.simpleMessage(
      "Available Stock Volume",
    ),
    "trading_average_cost_price": MessageLookupByLibrary.simpleMessage(
      "Average Cost Price",
    ),
    "trading_average_matching_price": MessageLookupByLibrary.simpleMessage(
      "Average matching price",
    ),
    "trading_bid_price_title": MessageLookupByLibrary.simpleMessage(
      "Giá đặt/\nGiá khớp",
    ),
    "trading_blocked_stock": MessageLookupByLibrary.simpleMessage(
      "Blocked Stock",
    ),
    "trading_buy": MessageLookupByLibrary.simpleMessage("Buy"),
    "trading_buy_order": MessageLookupByLibrary.simpleMessage("Buy order"),
    "trading_buy_order_title": MessageLookupByLibrary.simpleMessage(
      "Buy order",
    ),
    "trading_buying_power": MessageLookupByLibrary.simpleMessage(
      "Buying power",
    ),
    "trading_cancel_all_order": MessageLookupByLibrary.simpleMessage(
      "Cancel all order",
    ),
    "trading_cancel_all_orders_confirmation":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to cancel all orders?",
        ),
    "trading_cancel_order": MessageLookupByLibrary.simpleMessage(
      "Cancel order",
    ),
    "trading_cancel_order_confirm_message":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to cancel the selected order?",
        ),
    "trading_cancel_order_success": MessageLookupByLibrary.simpleMessage(
      "Order cancelled successfully",
    ),
    "trading_cancel_order_title": MessageLookupByLibrary.simpleMessage(
      "Cancel order",
    ),
    "trading_cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
    "trading_cancelled_2": MessageLookupByLibrary.simpleMessage("Cancelled"),
    "trading_cancelling": MessageLookupByLibrary.simpleMessage("Cancelling"),
    "trading_capital_cost_price": MessageLookupByLibrary.simpleMessage(
      "Average Cost Price",
    ),
    "trading_capital_value": MessageLookupByLibrary.simpleMessage(
      "Capital Value",
    ),
    "trading_category": MessageLookupByLibrary.simpleMessage("Category"),
    "trading_close": MessageLookupByLibrary.simpleMessage("Close"),
    "trading_code_title": MessageLookupByLibrary.simpleMessage("Mã"),
    "trading_collateral_stock": MessageLookupByLibrary.simpleMessage(
      "Collateral Stock",
    ),
    "trading_command_history": MessageLookupByLibrary.simpleMessage(
      "Command history",
    ),
    "trading_command_status": MessageLookupByLibrary.simpleMessage(
      "Command status",
    ),
    "trading_command_type": MessageLookupByLibrary.simpleMessage(
      "Command type",
    ),
    "trading_condition_dilution_choice": MessageLookupByLibrary.simpleMessage(
      "When there is an event of stock price dilution rights",
    ),
    "trading_condition_profit_margin": MessageLookupByLibrary.simpleMessage(
      "Profit margin",
    ),
    "trading_condition_slippage_margin": MessageLookupByLibrary.simpleMessage(
      "Slippage margin",
    ),
    "trading_condition_stop_loss_margin": MessageLookupByLibrary.simpleMessage(
      "Stop loss margin",
    ),
    "trading_condition_stop_loss_rate_percent":
        MessageLookupByLibrary.simpleMessage("Stop loss ratio (%)"),
    "trading_condition_take_profit_rate_percent":
        MessageLookupByLibrary.simpleMessage("Take Profit Ratio (%)"),
    "trading_conditional_order_popup_note":
        MessageLookupByLibrary.simpleMessage(
          "The order can only be activated once during its validity period.",
        ),
    "trading_cost_price": MessageLookupByLibrary.simpleMessage("Cost Price"),
    "trading_custom": MessageLookupByLibrary.simpleMessage("Custom"),
    "trading_deposit": MessageLookupByLibrary.simpleMessage("TK ký quỹ"),
    "trading_deposit_full": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản ký quỹ",
    ),
    "trading_edit_cancel_title": MessageLookupByLibrary.simpleMessage(
      "Edit/Cancel",
    ),
    "trading_edit_order": MessageLookupByLibrary.simpleMessage("Edit order"),
    "trading_edit_order_button": MessageLookupByLibrary.simpleMessage(
      "Edit order",
    ),
    "trading_edited": MessageLookupByLibrary.simpleMessage("Edited"),
    "trading_effective_time": MessageLookupByLibrary.simpleMessage(
      "Effective time",
    ),
    "trading_expected_profit_loss": MessageLookupByLibrary.simpleMessage(
      "Expected P/L",
    ),
    "trading_expire": MessageLookupByLibrary.simpleMessage("Expire"),
    "trading_expired": MessageLookupByLibrary.simpleMessage("Expired"),
    "trading_history_stock_code": MessageLookupByLibrary.simpleMessage(
      "Stock code",
    ),
    "trading_holding_portfolio": MessageLookupByLibrary.simpleMessage(
      "Holding Portfolio",
    ),
    "trading_investment_principal": MessageLookupByLibrary.simpleMessage(
      "Investment Principal",
    ),
    "trading_joint_volume": MessageLookupByLibrary.simpleMessage(
      "Joint volume",
    ),
    "trading_market_value": MessageLookupByLibrary.simpleMessage(
      "Market Value",
    ),
    "trading_match_all": MessageLookupByLibrary.simpleMessage("Match all"),
    "trading_matched_complete": MessageLookupByLibrary.simpleMessage("Matched"),
    "trading_matched_partial": MessageLookupByLibrary.simpleMessage(
      "Partially matched",
    ),
    "trading_matched_price": MessageLookupByLibrary.simpleMessage(
      "Matched price",
    ),
    "trading_max_volume": MessageLookupByLibrary.simpleMessage(
      "Maximum buy volume",
    ),
    "trading_max_volume_sell": MessageLookupByLibrary.simpleMessage(
      "Maximum sell volume",
    ),
    "trading_modified": MessageLookupByLibrary.simpleMessage("Modified"),
    "trading_modifying": MessageLookupByLibrary.simpleMessage("Modifying"),
    "trading_month": MessageLookupByLibrary.simpleMessage("month"),
    "trading_no_data": MessageLookupByLibrary.simpleMessage(
      "Hiện tại không có dữ liệu",
    ),
    "trading_no_data_message": MessageLookupByLibrary.simpleMessage("No data"),
    "trading_no_filter": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "trading_normal": MessageLookupByLibrary.simpleMessage("Normal"),
    "trading_order_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt",
    ),
    "trading_order_description_atc": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa",
    ),
    "trading_order_description_ato": MessageLookupByLibrary.simpleMessage(
      "Lệnh tranh mua bán tại mức giá mở cửa",
    ),
    "trading_order_description_buyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00",
    ),
    "trading_order_description_gtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập",
    ),
    "trading_order_description_lo": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán theo giá mong muốn",
    ),
    "trading_order_description_mak": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh",
    ),
    "trading_order_description_mok": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập",
    ),
    "trading_order_description_mp": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch",
    ),
    "trading_order_description_mtl": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO",
    ),
    "trading_order_description_plo": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC",
    ),
    "trading_order_price": MessageLookupByLibrary.simpleMessage("Order price"),
    "trading_order_price_invalid": MessageLookupByLibrary.simpleMessage(
      "Order price invalid",
    ),
    "trading_order_stop_loss": MessageLookupByLibrary.simpleMessage(
      "Order stop loss",
    ),
    "trading_order_take_profit": MessageLookupByLibrary.simpleMessage(
      "Order take profit",
    ),
    "trading_order_time": MessageLookupByLibrary.simpleMessage("Order time"),
    "trading_order_type_atc": MessageLookupByLibrary.simpleMessage("Lệnh ATC"),
    "trading_order_type_ato": MessageLookupByLibrary.simpleMessage("Lệnh ATO"),
    "trading_order_type_buyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh Buy-in",
    ),
    "trading_order_type_condition": MessageLookupByLibrary.simpleMessage(
      "Lệnh điều kiện",
    ),
    "trading_order_type_filter": MessageLookupByLibrary.simpleMessage(
      "Order Type",
    ),
    "trading_order_type_gtc": MessageLookupByLibrary.simpleMessage("Lệnh GTC"),
    "trading_order_type_lo": MessageLookupByLibrary.simpleMessage(
      "Lệnh thường",
    ),
    "trading_order_type_mak": MessageLookupByLibrary.simpleMessage("Lệnh MAK"),
    "trading_order_type_mok": MessageLookupByLibrary.simpleMessage("Lệnh MOK"),
    "trading_order_type_mp": MessageLookupByLibrary.simpleMessage("Lệnh MP"),
    "trading_order_type_mtl": MessageLookupByLibrary.simpleMessage("Lệnh MTL"),
    "trading_order_type_plo": MessageLookupByLibrary.simpleMessage("Lệnh PLO"),
    "trading_order_value": MessageLookupByLibrary.simpleMessage("Order value"),
    "trading_order_value_2": MessageLookupByLibrary.simpleMessage(
      "Order value",
    ),
    "trading_order_value_title": MessageLookupByLibrary.simpleMessage(
      "Order value",
    ),
    "trading_ordinary": MessageLookupByLibrary.simpleMessage("TK thường"),
    "trading_ordinary_full": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản thường",
    ),
    "trading_party_trade": MessageLookupByLibrary.simpleMessage("Matched"),
    "trading_pending_order": MessageLookupByLibrary.simpleMessage(
      "List of pending orders",
    ),
    "trading_pending_rights": MessageLookupByLibrary.simpleMessage(
      "Pending Rights",
    ),
    "trading_pending_stock": MessageLookupByLibrary.simpleMessage(
      "Pending Stock",
    ),
    "trading_place_order_price": MessageLookupByLibrary.simpleMessage("Giá"),
    "trading_place_stock_order": MessageLookupByLibrary.simpleMessage(
      "Place a stock order",
    ),
    "trading_portfolio_weight": MessageLookupByLibrary.simpleMessage(
      "Portfolio Weight",
    ),
    "trading_price": MessageLookupByLibrary.simpleMessage("Price"),
    "trading_price_list": MessageLookupByLibrary.simpleMessage("Price list"),
    "trading_price_price": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "trading_processing": MessageLookupByLibrary.simpleMessage("Processing"),
    "trading_rejected": MessageLookupByLibrary.simpleMessage("Rejected"),
    "trading_reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "trading_restricted_stock": MessageLookupByLibrary.simpleMessage(
      "Restricted Stock",
    ),
    "trading_sCode": MessageLookupByLibrary.simpleMessage("Stock code"),
    "trading_save_command": MessageLookupByLibrary.simpleMessage(
      "Lưu thông tin lệnh cho lần sau",
    ),
    "trading_search_by_stock_code": MessageLookupByLibrary.simpleMessage(
      "Search by stock code",
    ),
    "trading_selected": MessageLookupByLibrary.simpleMessage("Selected"),
    "trading_sell": MessageLookupByLibrary.simpleMessage("Sell"),
    "trading_sell_order": MessageLookupByLibrary.simpleMessage("Sell order"),
    "trading_sending": MessageLookupByLibrary.simpleMessage("Sending"),
    "trading_sent": MessageLookupByLibrary.simpleMessage("Sent"),
    "trading_showMessageNextTime": MessageLookupByLibrary.simpleMessage(
      "Hiển thị thông báo này trong lần sau",
    ),
    "trading_status": MessageLookupByLibrary.simpleMessage("Status"),
    "trading_status_all": MessageLookupByLibrary.simpleMessage("Status: All"),
    "trading_status_filter_title": MessageLookupByLibrary.simpleMessage(
      "Status",
    ),
    "trading_status_hint": MessageLookupByLibrary.simpleMessage("Status"),
    "trading_status_selected": MessageLookupByLibrary.simpleMessage(
      "Status: Selected",
    ),
    "trading_status_title": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "trading_stock_code": MessageLookupByLibrary.simpleMessage("Stock Code"),
    "trading_stock_symbol": MessageLookupByLibrary.simpleMessage(
      "Stock Symbol",
    ),
    "trading_stock_type": MessageLookupByLibrary.simpleMessage("Stock type"),
    "trading_stop_loss": MessageLookupByLibrary.simpleMessage("Cắt lỗ"),
    "trading_sub_account": MessageLookupByLibrary.simpleMessage("Sub-account"),
    "trading_sub_account_hint": MessageLookupByLibrary.simpleMessage(
      "Sub-account",
    ),
    "trading_sub_command": MessageLookupByLibrary.simpleMessage("Sub command"),
    "trading_take_profit": MessageLookupByLibrary.simpleMessage("Chốt lời"),
    "trading_take_profit_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT",
    ),
    "trading_time": MessageLookupByLibrary.simpleMessage("Time"),
    "trading_total_stock_volume": MessageLookupByLibrary.simpleMessage(
      "Total Stock Volume",
    ),
    "trading_transaction_history": MessageLookupByLibrary.simpleMessage(
      "Transaction history",
    ),
    "trading_transaction_type": MessageLookupByLibrary.simpleMessage(
      "Transaction type",
    ),
    "trading_update_order_success": MessageLookupByLibrary.simpleMessage(
      "Order updated successfully",
    ),
    "trading_utilities": MessageLookupByLibrary.simpleMessage("Utilities"),
    "trading_value": MessageLookupByLibrary.simpleMessage("Giá trị"),
    "trading_vol_title": MessageLookupByLibrary.simpleMessage(
      "KL đặt/\nKL khớp",
    ),
    "trading_volume": MessageLookupByLibrary.simpleMessage("Volume"),
    "trading_volume_holding": MessageLookupByLibrary.simpleMessage(
      "Volume of holdings",
    ),
    "trading_volume_title": MessageLookupByLibrary.simpleMessage("Volume"),
    "trading_waiting": MessageLookupByLibrary.simpleMessage("Waiting"),
    "trading_waiting_command": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "trading_waiting_match": MessageLookupByLibrary.simpleMessage(
      "Waiting for match",
    ),
    "trading_waiting_send": MessageLookupByLibrary.simpleMessage(
      "Wait for send",
    ),
    "trading_waiting_to_send": MessageLookupByLibrary.simpleMessage(
      "Wait for send",
    ),
    "trading_year": MessageLookupByLibrary.simpleMessage("year"),
    "traing_stop_loss_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT",
    ),
  };
}
